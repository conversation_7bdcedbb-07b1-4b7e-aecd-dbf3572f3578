'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PageHeader } from '@/components/PageHeader';
import { useAuth } from '@/components/AuthProvider';
import { isAdmin } from '@/lib/auth-supabase';

export default function TestOperationsPage() {
  const { user } = useAuth();
  const userIsAdmin = isAdmin(user);
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testCreateUser = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-user-operations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          username: 'testuser' + Date.now(),
          fullName: 'مستخدم تجريبي',
          email: '<EMAIL>',
          password: 'test123',
          role: 'editor'
        })
      });
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testListUsers = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-user-operations');
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testDeleteUser = async () => {
    const userId = prompt('أدخل ID المستخدم للحذف:');
    if (!userId) return;

    setLoading(true);
    try {
      const response = await fetch('/api/test-user-operations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'delete',
          userId
        })
      });
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader 
        title="اختبار العمليات" 
        description="اختبار وظائف إنشاء وحذف المستخدمين"
      />

      <Card>
        <CardHeader>
          <CardTitle>معلومات المستخدم الحالي</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p><strong>اسم المستخدم:</strong> {user?.username || 'غير مسجل دخول'}</p>
            <p><strong>الدور:</strong> {user?.role || 'غير محدد'}</p>
            <p><strong>هل إداري؟</strong> {userIsAdmin ? 'نعم' : 'لا'}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>اختبار العمليات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Button 
                onClick={testListUsers}
                disabled={loading}
                variant="outline"
              >
                عرض جميع المستخدمين
              </Button>
              
              <Button 
                onClick={testCreateUser}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700"
              >
                إنشاء مستخدم تجريبي
              </Button>
              
              <Button 
                onClick={testDeleteUser}
                disabled={loading}
                variant="destructive"
              >
                حذف مستخدم
              </Button>
            </div>

            {loading && (
              <p className="text-blue-600">جاري التحميل...</p>
            )}

            {result && (
              <div className="mt-4">
                <h3 className="font-medium mb-2">النتيجة:</h3>
                <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="text-yellow-800">ملاحظات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-yellow-700">
            <p>• هذه الصفحة لاختبار وظائف إدارة المستخدمين</p>
            <p>• تأكد من تسجيل الدخول كإداري لرؤية جميع الوظائف</p>
            <p>• المستخدمين التجريبيين سيتم إنشاؤهم بأسماء فريدة</p>
            <p>• احذر عند حذف المستخدمين - العملية لا يمكن التراجع عنها</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
