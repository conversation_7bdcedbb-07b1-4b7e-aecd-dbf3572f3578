import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath, revalidateTag } from 'next/cache';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { path, tag, secret } = body;

    // Verify the secret to prevent unauthorized revalidation
    if (secret !== process.env.REVALIDATION_SECRET) {
      return NextResponse.json(
        { error: 'Invalid secret' },
        { status: 401 }
      );
    }

    if (path) {
      // Revalidate specific path
      revalidatePath(path);
      console.log(`Revalidated path: ${path}`);
    }

    if (tag) {
      // Revalidate by tag
      revalidateTag(tag);
      console.log(`Revalidated tag: ${tag}`);
    }

    // If no specific path or tag, revalidate articles
    if (!path && !tag) {
      revalidatePath('/articles/[slug]', 'page');
      console.log('Revalidated all article pages');
    }

    return NextResponse.json(
      { message: 'Revalidation successful' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Revalidation error:', error);
    return NextResponse.json(
      { error: 'Revalidation failed' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const path = searchParams.get('path');
  const secret = searchParams.get('secret');

  // Verify the secret
  if (secret !== process.env.REVALIDATION_SECRET) {
    return NextResponse.json(
      { error: 'Invalid secret' },
      { status: 401 }
    );
  }

  try {
    if (path) {
      revalidatePath(path);
      console.log(`Revalidated path: ${path}`);
    } else {
      // Revalidate all article pages
      revalidatePath('/articles/[slug]', 'page');
      console.log('Revalidated all article pages');
    }

    return NextResponse.json(
      { message: 'Revalidation successful' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Revalidation error:', error);
    return NextResponse.json(
      { error: 'Revalidation failed' },
      { status: 500 }
    );
  }
}
