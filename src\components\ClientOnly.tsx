'use client';

import { useEffect, useState } from 'react';

interface ClientOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Hook for safe date formatting that prevents hydration mismatches
export function useSafeDate() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const formatDate = (date: Date, options?: Intl.DateTimeFormatOptions) => {
    if (!mounted) return '';

    try {
      return new Intl.DateTimeFormat('ar-SA-u-ca-gregory-nu-latn', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        ...options
      }).format(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return date.toLocaleDateString();
    }
  };

  const formatHijriDate = (date: Date, options?: Intl.DateTimeFormatOptions) => {
    if (!mounted) return '';

    try {
      // استخدام التقويم الإسلامي مع تنسيق أفضل
      const formatter = new Intl.DateTimeFormat('ar-SA', {
        calendar: 'islamic',
        numberingSystem: 'latn',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        ...options
      });

      const formatted = formatter.format(date);
      // إضافة "هـ" في النهاية إذا لم تكن موجودة
      return formatted.includes('هـ') ? formatted : formatted + ' هـ';
    } catch (error) {
      console.error('Error formatting Hijri date:', error);
      return date.toLocaleDateString();
    }
  };

  return { formatDate, formatHijriDate, mounted };
}
