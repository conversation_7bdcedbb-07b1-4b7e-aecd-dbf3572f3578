'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import HijriDate from 'hijri-date';
import { ArrowDown, RotateCcw } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// --- Validation Schemas ---
const dateSchema = z.object({
  day: z.coerce.number().min(1).max(31),
  month: z.coerce.number().min(1).max(12),
  year: z.coerce.number().min(1),
});

// --- Reusable Components ---
const DateFields = ({ control, isHijri = false }: { control: any, isHijri?: boolean }) => {
  const currentYear = new Date().getFullYear();
  const years = isHijri 
    ? Array.from({ length: 201 }, (_, i) => new HijriDate().getFullYear() - 100 + i)
    : Array.from({ length: 201 }, (_, i) => currentYear - 100 + i);
  const months = Array.from({ length: 12 }, (_, i) => i + 1);
  const days = Array.from({ length: 31 }, (_, i) => i + 1);

  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <FormField
            control={control}
            name="day"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>اليوم</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={String(field.value)}>
                        <FormControl><SelectTrigger><SelectValue placeholder="يوم" /></SelectTrigger></FormControl>
                        <SelectContent>
                            {days.map(day => <SelectItem key={day} value={String(day)}>{day}</SelectItem>)}
                        </SelectContent>
                    </Select>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="month"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>الشهر</FormLabel>
                     <Select onValueChange={field.onChange} defaultValue={String(field.value)}>
                        <FormControl><SelectTrigger><SelectValue placeholder="شهر" /></SelectTrigger></FormControl>
                        <SelectContent>
                            {months.map(month => <SelectItem key={month} value={String(month)}>{month}</SelectItem>)}
                        </SelectContent>
                    </Select>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="year"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>السنة</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={String(field.value)}>
                        <FormControl><SelectTrigger><SelectValue placeholder="سنة" /></SelectTrigger></FormControl>
                        <SelectContent>
                            {years.map(year => <SelectItem key={year} value={String(year)}>{year}</SelectItem>)}
                        </SelectContent>
                    </Select>
                    <FormMessage />
                </FormItem>
            )}
        />
    </div>
  );
};

// --- Main Tool Component ---
export function DateConverterTool() {
  const [hijriResult, setHijriResult] = useState<string>('');
  const [gregorianResult, setGregorianResult] = useState<string>('');

  const clearResults = () => {
    setHijriResult('');
    setGregorianResult('');
    gregorianForm.reset({day: undefined, month: undefined, year: undefined});
    hijriForm.reset({day: undefined, month: undefined, year: undefined});
  };

  const gregorianForm = useForm<z.infer<typeof dateSchema>>({
    resolver: zodResolver(dateSchema.refine(data => {
        try {
            const d = new Date(data.year, data.month - 1, data.day);
            return d.getFullYear() === data.year && d.getMonth() === data.month - 1;
        } catch { return false; }
    }, {message: "تاريخ ميلادي غير صالح", path: ["day"]})),
  });

  const hijriForm = useForm<z.infer<typeof dateSchema>>({
    resolver: zodResolver(dateSchema.refine(data => {
        try {
            new HijriDate(data.year, data.month, data.day).toGregorian();
            return true;
        } catch { return false; }
    }, {message: "تاريخ هجري غير صالح", path: ["day"]})),
  });

  const onGregorianSubmit = (data: z.infer<typeof dateSchema>) => {
    try {
      const gregorianDate = new Date(data.year, data.month - 1, data.day);

      if (typeof window !== 'undefined') {
        const formatter = new Intl.DateTimeFormat('ar-SA', {
          calendar: 'islamic',
          numberingSystem: 'latn',
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        });

        const formatted = formatter.format(gregorianDate);
        const formattedHijri = formatted.includes('هـ') ? formatted : formatted + ' هـ';
        setHijriResult(formattedHijri);
      } else {
        setHijriResult('جاري التحويل...');
      }
    } catch (error) {
      console.error('Error converting Gregorian to Hijri:', error);
      setHijriResult('خطأ في التحويل - تأكد من صحة التاريخ المدخل');
    }
  };

  const onHijriSubmit = (data: z.infer<typeof dateSchema>) => {
    try {
      const hijriDate = new HijriDate(data.year, data.month, data.day);
      const gregorian = hijriDate.toGregorian();

      if (typeof window !== 'undefined') {
        const formattedGregorian = new Intl.DateTimeFormat('ar-SA-u-ca-gregory-nu-latn', {
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        }).format(gregorian);
        setGregorianResult(formattedGregorian + 'م');
      } else {
        setGregorianResult('جاري التحويل...');
      }
    } catch (error) {
      console.error('Error converting Hijri to Gregorian:', error);
      setGregorianResult('خطأ في التحويل - تأكد من صحة التاريخ المدخل');
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          محول التاريخ
          {(hijriResult || gregorianResult) && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearResults}
              className="text-xs"
            >
              <RotateCcw className="ml-1 h-3 w-3" />
              مسح النتائج
            </Button>
          )}
        </CardTitle>
        <CardDescription>
          التحويل بين التاريخ الميلادي والهجري بسهولة ودقة. أدخل التاريخ في أي من النظامين للحصول على التاريخ المقابل.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">

        {/* From Gregorian to Hijri */}
        <div className="p-6 border rounded-lg bg-muted/30">
          <h3 className="text-lg font-semibold mb-4">من ميلادي إلى هجري</h3>
          <Form {...gregorianForm}>
            <form onSubmit={gregorianForm.handleSubmit(onGregorianSubmit)} className="space-y-4">
              <DateFields control={gregorianForm.control} isHijri={false} />
              <Button type="submit" className="w-full">
                <ArrowDown className="ml-2 h-4 w-4" />
                تحويل إلى هجري
              </Button>
            </form>
          </Form>
          {hijriResult && (
            <div className="mt-4 p-4 bg-primary/10 rounded-lg w-full text-center">
              <p className="text-sm text-muted-foreground">التاريخ الهجري الموافق</p>
              <p className="text-xl font-bold font-mono text-primary">{hijriResult}</p>
            </div>
          )}
        </div>

        {/* From Hijri to Gregorian */}
        <div className="p-6 border rounded-lg bg-muted/30">
          <h3 className="text-lg font-semibold mb-4">من هجري إلى ميلادي</h3>
          <Form {...hijriForm}>
            <form onSubmit={hijriForm.handleSubmit(onHijriSubmit)} className="space-y-4">
              <DateFields control={hijriForm.control} isHijri={true} />
              <Button type="submit" className="w-full">
                <ArrowDown className="ml-2 h-4 w-4" />
                تحويل إلى ميلادي
              </Button>
            </form>
          </Form>
          {gregorianResult && (
            <div className="mt-4 p-4 bg-primary/10 rounded-lg w-full text-center">
              <p className="text-sm text-muted-foreground">التاريخ الميلادي الموافق</p>
              <p className="text-xl font-bold font-mono text-primary">{gregorianResult}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
