'use client';

import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { PlusCircle, Trash2, Calculator, Info, RotateCcw } from 'lucide-react';
import { useState } from 'react';

const requiredNumber = (message = "هذا الحقل مطلوب.") =>
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const subjectSchema = z.object({
  name: z.string().min(1, 'اسم المادة مطلوب.'),
  grade: requiredNumber().min(0, "الدرجة لا يمكن أن تكون سالبة."),
  maxGrade: z.number().min(1, "الدرجة العظمى مطلوبة."),
});

const formSchema = z.object({
  studentType: z.enum(['muslim', 'christian'], {
    required_error: "يرجى اختيار نوع الطالب.",
  }),
  subjects: z.array(subjectSchema).min(1, 'يجب إضافة مادة واحدة على الأقل.'),
});

type FormValues = z.infer<typeof formSchema>;

// المواد الأساسية لنظام التوجيهي 2008 الجديد
const initialSubjectsMuslim = [
  { name: 'التربية الإسلامية', maxGrade: 60 },
  { name: 'اللغة العربية', maxGrade: 100 },
  { name: 'اللغة الإنجليزية', maxGrade: 100 },
  { name: 'تاريخ الأردن', maxGrade: 40 },
];

const initialSubjectsChristian = [
  { name: 'اللغة العربية', maxGrade: 100 },
  { name: 'اللغة الإنجليزية', maxGrade: 100 },
  { name: 'تاريخ الأردن', maxGrade: 40 },
];

export function Tawjihi2008CalculatorTool() {
  const [result, setResult] = useState<{
    finalGrade: number;
    totalScore: number;
    maxPossible: number;
    percentage: number;
    studentType: string;
  } | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      studentType: 'muslim',
      subjects: initialSubjectsMuslim.map(s => ({...s, grade: undefined})),
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "subjects",
  });

  const studentType = form.watch('studentType');

  // تحديث المواد عند تغيير نوع الطالب
  const updateSubjectsForStudentType = (type: 'muslim' | 'christian') => {
    const newSubjects = type === 'muslim' ? initialSubjectsMuslim : initialSubjectsChristian;
    form.setValue('subjects', newSubjects.map(s => ({...s, grade: undefined})));
    setResult(null);
  };

  const onSubmit = (data: FormValues) => {
    const totalScore = data.subjects.reduce((sum, subject) => sum + subject.grade, 0);
    const maxPossible = data.subjects.reduce((sum, subject) => sum + subject.maxGrade, 0);

    // حساب الدرجة النهائية حسب النظام الجديد
    let finalGrade: number;
    if (data.studentType === 'muslim') {
      // للطالب المسلم: المجموع ÷ 10
      finalGrade = totalScore / 10;
    } else {
      // للطالب المسيحي: المجموع ÷ 8
      finalGrade = totalScore / 8;
    }

    const percentage = (totalScore / maxPossible) * 100;

    setResult({
      finalGrade,
      totalScore,
      maxPossible,
      percentage,
      studentType: data.studentType
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          حاسبة معدل التوجيهي 2008 - النظام الجديد
        </CardTitle>
        <CardDescription>
          احسب درجتك النهائية في امتحانات التوجيهي 2008 حسب النظام الجديد (30% من المعدل النهائي).
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-start gap-2">
            <Info className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-amber-800 mb-2">معلومات مهمة حول النظام الجديد:</h4>
              <ul className="text-amber-700 text-sm space-y-1">
                <li>• هذه النتائج تمثل <strong>30% فقط</strong> من المعدل النهائي للتوجيهي</li>
                <li>• العام المقبل سيشكل 70% من المعدل النهائي</li>
                <li>• النظام يشمل 4 مباحث أساسية بعلامات عظمى مختلفة</li>
                <li>• آلية الحساب تختلف حسب نوع الطالب (مسلم/مسيحي)</li>
              </ul>
            </div>
          </div>
        </div>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* اختيار نوع الطالب */}
            <FormField
              control={form.control}
              name="studentType"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-base font-semibold">نوع الطالب</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                        updateSubjectsForStudentType(value as 'muslim' | 'christian');
                      }}
                      value={field.value}
                      className="flex flex-row space-x-6 space-x-reverse"
                    >
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RadioGroupItem value="muslim" id="muslim" />
                        <Label htmlFor="muslim" className="cursor-pointer">طالب مسلم</Label>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RadioGroupItem value="christian" id="christian" />
                        <Label htmlFor="christian" className="cursor-pointer">طالب مسيحي</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <div className="grid grid-cols-[1fr,100px,80px,auto] gap-2 items-center text-sm font-medium text-muted-foreground border-b pb-2">
                <span>اسم المادة</span>
                <span className="text-center">الدرجة</span>
                <span className="text-center">من</span>
                <span className="text-center">حذف</span>
              </div>
              {fields.map((field, index) => (
                <div key={field.id} className="grid grid-cols-[1fr,100px,80px,auto] gap-2 items-start">
                  <FormField
                    name={`subjects.${index}.name`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder={`مادة ${index + 1}`}
                            {...field}
                            className="text-right"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    name={`subjects.${index}.grade`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            max={form.getValues(`subjects.${index}.maxGrade`)}
                            placeholder="0"
                            {...field}
                            className="text-center"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    name={`subjects.${index}.maxGrade`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            {...field}
                            className="text-center bg-gray-50"
                            readOnly
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="text-center">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => remove(index)}
                      className="text-destructive hover:text-destructive"
                      disabled={fields.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => append({ name: '', grade: undefined, maxGrade: 100 })}
                className="flex-1 border-dashed"
              >
                <PlusCircle className="ml-2 h-4 w-4" />
                إضافة مادة
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  // إعادة تعيين النموذج للمواد الافتراضية
                  const currentType = form.getValues('studentType');
                  const defaultSubjects = currentType === 'muslim' ? initialSubjectsMuslim : initialSubjectsChristian;
                  form.reset({
                    studentType: currentType,
                    subjects: defaultSubjects.map(s => ({...s, grade: undefined}))
                  });
                  setResult(null);
                }}
                className="px-4"
              >
                <RotateCcw className="ml-2 h-4 w-4" />
                إعادة تعيين
              </Button>
            </div>

            <div className="flex flex-col items-center space-y-4 pt-6 border-t">
              <Button type="submit" size="lg" className="w-full sm:w-auto px-12">
                <Calculator className="ml-2 h-4 w-4" />
                احسب المعدل
              </Button>
              
              {result && (
                <div className="space-y-4 w-full">
                  {/* الدرجة النهائية - الأهم */}
                  <div className="text-center p-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200">
                    <p className="text-sm text-blue-700 mb-2">الدرجة النهائية (30% من المعدل الكلي)</p>
                    <p className="text-6xl font-bold text-blue-800 font-mono mb-2">
                      {result.finalGrade.toFixed(2)}
                    </p>
                    <p className="text-xs text-blue-600">
                      {result.studentType === 'muslim' ? 'حُسبت بقسمة المجموع على 10' : 'حُسبت بقسمة المجموع على 8'}
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="text-center p-6 bg-green-50 rounded-lg border border-green-200">
                      <p className="text-sm text-green-700 mb-2">المجموع الكلي</p>
                      <p className="text-3xl font-bold text-green-800 font-mono">
                        {result.totalScore}
                      </p>
                      <p className="text-xs text-green-600 mt-1">من {result.maxPossible}</p>
                    </div>

                    <div className="text-center p-6 bg-purple-50 rounded-lg border border-purple-200">
                      <p className="text-sm text-purple-700 mb-2">النسبة المئوية</p>
                      <p className="text-3xl font-bold text-purple-800 font-mono">
                        {result.percentage.toFixed(1)}%
                      </p>
                    </div>
                  </div>

                  {/* تذكير مهم */}
                  <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="flex items-start gap-2">
                      <Info className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-amber-800 mb-1">تذكير مهم:</p>
                        <p className="text-sm text-amber-700">
                          هذه النتيجة تمثل <strong>30% فقط</strong> من معدلك النهائي في التوجيهي.
                          العام المقبل سيشكل 70% من المعدل النهائي.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* تقييم الأداء */}
                  <div className="p-4 rounded-lg border" style={{
                    backgroundColor: result.percentage >= 90 ? '#dcfce7' :
                                   result.percentage >= 80 ? '#fef3c7' :
                                   result.percentage >= 70 ? '#fed7aa' : '#fecaca',
                    borderColor: result.percentage >= 90 ? '#16a34a' :
                                result.percentage >= 80 ? '#d97706' :
                                result.percentage >= 70 ? '#ea580c' : '#dc2626'
                  }}>
                    <p className="text-sm font-medium mb-1">تقييم الأداء في هذا الجزء:</p>
                    <p className="font-semibold">
                      {result.percentage >= 90 ? '🌟 ممتاز - أداء متميز!' :
                       result.percentage >= 80 ? '👍 جيد جداً - أداء قوي' :
                       result.percentage >= 70 ? '✅ جيد - أداء مقبول' :
                       result.percentage >= 60 ? '⚠️ مقبول - يحتاج تحسين' :
                       '❌ ضعيف - يحتاج جهد أكبر'}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
