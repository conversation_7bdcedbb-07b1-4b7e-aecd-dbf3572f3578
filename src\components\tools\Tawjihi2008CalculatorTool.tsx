'use client';

import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { PlusCircle, Trash2, Calculator, Info, RotateCcw } from 'lucide-react';
import { useState } from 'react';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const subjectSchema = z.object({
  name: z.string().min(1, 'اسم المادة مطلوب.'),
  grade: requiredNumber().min(0, "الدرجة لا يمكن أن تكون سالبة.").max(100, "الدرجة لا يمكن أن تزيد عن 100."),
});

const formSchema = z.object({
  subjects: z.array(subjectSchema).min(1, 'يجب إضافة مادة واحدة على الأقل.'),
});

type FormValues = z.infer<typeof formSchema>;

// المواد الأساسية لنظام التوجيهي 2008
const initialSubjects2008 = [
  { name: 'التربية الإسلامية' },
  { name: 'اللغة العربية' },
  { name: 'اللغة الإنجليزية' },
  { name: 'الرياضيات' },
  { name: 'الفيزياء' },
  { name: 'الكيمياء' },
  { name: 'الأحياء' },
  { name: 'علوم الأرض والبيئة' },
];

export function Tawjihi2008CalculatorTool() {
  const [result, setResult] = useState<{ 
    average: number; 
    totalScore: number; 
    maxPossible: number;
    percentage: number;
  } | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      subjects: initialSubjects2008.map(s => ({...s, grade: undefined})),
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "subjects",
  });

  const onSubmit = (data: FormValues) => {
    const totalScore = data.subjects.reduce((sum, subject) => sum + subject.grade, 0);
    const average = totalScore / data.subjects.length;
    const maxPossible = data.subjects.length * 100;
    const percentage = (totalScore / maxPossible) * 100;
    
    setResult({ 
      average, 
      totalScore, 
      maxPossible,
      percentage 
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          حاسبة معدل التوجيهي 2008
        </CardTitle>
        <CardDescription>
          أدخل درجاتك في المواد لحساب معدلك النهائي في التوجيهي لعام 2008 (النظام القديم).
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-2">
            <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-blue-900 mb-2">ملاحظات مهمة حول نظام 2008:</h4>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• هذه الحاسبة مخصصة لنظام التوجيهي الأردني لعام 2008 والسنوات المشابهة</li>
                <li>• يرجى إدخال الدرجات كما هي مكتوبة في الشهادة (من 100 لكل مادة)</li>
                <li>• يمكنك إضافة أو حذف المواد حسب ما هو موجود في شهادتك</li>
                <li>• النتيجة تعطيك المعدل العام والنسبة المئوية الإجمالية</li>
              </ul>
            </div>
          </div>
        </div>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <div className="grid grid-cols-[1fr,120px,auto] gap-2 items-center text-sm font-medium text-muted-foreground border-b pb-2">
                <span>اسم المادة</span>
                <span className="text-center">الدرجة (من 100)</span>
                <span className="text-center">حذف</span>
              </div>
              {fields.map((field, index) => (
                <div key={field.id} className="grid grid-cols-[1fr,120px,auto] gap-2 items-start">
                  <FormField
                    name={`subjects.${index}.name`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input 
                            placeholder={`مادة ${index + 1}`} 
                            {...field}
                            className="text-right"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    name={`subjects.${index}.grade`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="100" 
                            placeholder="85" 
                            {...field}
                            className="text-center"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="text-center">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => remove(index)}
                      className="text-destructive hover:text-destructive"
                      disabled={fields.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => append({ name: '', grade: undefined })}
                className="flex-1 border-dashed"
              >
                <PlusCircle className="ml-2 h-4 w-4" />
                إضافة مادة
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  // إعادة تعيين النموذج للمواد الافتراضية
                  form.reset({
                    subjects: initialSubjects2008.map(s => ({...s, grade: undefined}))
                  });
                  setResult(null);
                }}
                className="px-4"
              >
                <RotateCcw className="ml-2 h-4 w-4" />
                إعادة تعيين
              </Button>
            </div>

            <div className="flex flex-col items-center space-y-4 pt-6 border-t">
              <Button type="submit" size="lg" className="w-full sm:w-auto px-12">
                <Calculator className="ml-2 h-4 w-4" />
                احسب المعدل
              </Button>
              
              {result && (
                <div className="space-y-4 w-full">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="text-center p-6 bg-primary/10 rounded-lg border-2 border-primary/20">
                      <p className="text-sm text-primary/80 mb-2">المعدل العام</p>
                      <p className="text-4xl font-bold text-primary font-mono">
                        {result.average.toFixed(2)}%
                      </p>
                    </div>

                    <div className="text-center p-6 bg-green-50 rounded-lg border border-green-200">
                      <p className="text-sm text-green-700 mb-2">النسبة المئوية</p>
                      <p className="text-4xl font-bold text-green-800 font-mono">
                        {result.percentage.toFixed(2)}%
                      </p>
                    </div>
                  </div>

                  <div className="text-center p-4 bg-gray-50 rounded-lg border">
                    <p className="text-sm text-gray-600 mb-1">المجموع الكلي</p>
                    <p className="text-lg font-semibold text-gray-800">
                      {result.totalScore} من {result.maxPossible}
                    </p>
                  </div>

                  {/* تقييم الأداء */}
                  <div className="p-4 rounded-lg border" style={{
                    backgroundColor: result.percentage >= 90 ? '#dcfce7' :
                                   result.percentage >= 80 ? '#fef3c7' :
                                   result.percentage >= 70 ? '#fed7aa' : '#fecaca',
                    borderColor: result.percentage >= 90 ? '#16a34a' :
                                result.percentage >= 80 ? '#d97706' :
                                result.percentage >= 70 ? '#ea580c' : '#dc2626'
                  }}>
                    <p className="text-sm font-medium mb-1">تقييم الأداء:</p>
                    <p className="font-semibold">
                      {result.percentage >= 90 ? '🌟 ممتاز - أداء متميز!' :
                       result.percentage >= 80 ? '👍 جيد جداً - أداء قوي' :
                       result.percentage >= 70 ? '✅ جيد - أداء مقبول' :
                       result.percentage >= 60 ? '⚠️ مقبول - يحتاج تحسين' :
                       '❌ ضعيف - يحتاج جهد أكبر'}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
