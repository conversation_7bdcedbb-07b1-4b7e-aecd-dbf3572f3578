'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ArrowDown, ArrowUp, AlertTriangle, TrendingUp } from 'lucide-react';
import type { getAramcoStock } from '@/lib/actions/aramco';

type StockData = Awaited<ReturnType<typeof getAramcoStock>>;

interface AramcoStockToolProps {
  initialData: StockData;
}

export function AramcoStockTool({ initialData }: AramcoStockToolProps) {
  const { success, error, ...stockData } = initialData;

  if (!success) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>خطأ في تحميل البيانات</AlertTitle>
        <AlertDescription>
          لم نتمكن من تحميل بيانات سهم أرامكو. الرجاء المحاولة مرة أخرى لاحقًا.
          <p className="mt-2 text-xs">تفاصيل الخطأ: {error}</p>
        </AlertDescription>
      </Alert>
    );
  }

  const isPositive = stockData.change >= 0;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp className="text-blue-500" />
            <span>{stockData.name} ({stockData.symbol})</span>
          </div>
          <span className={`text-3xl font-bold font-mono ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
            {stockData.price.toFixed(2)}
          </span>
        </CardTitle>
        <CardDescription className="flex items-center justify-between">
            <div>
              <span>تداول السعودية</span>
              {error && (
                <span className="block text-amber-600 text-xs mt-1">تنبيه: {error}</span>
              )}
            </div>
            <div className={`flex items-center gap-2 font-mono ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
                <span>{isPositive ? '+' : ''}{stockData.change.toFixed(2)}</span>
                <span>({isPositive ? '+' : ''}{stockData.changePercent.toFixed(2)}%)</span>
                {isPositive ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
            </div>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
          <div className="p-3 bg-secondary/50 rounded-lg">
            <p className="text-sm text-muted-foreground">افتتاح</p>
            <p className="font-mono font-semibold text-lg">{stockData.open.toFixed(2)}</p>
          </div>
          <div className="p-3 bg-secondary/50 rounded-lg">
            <p className="text-sm text-muted-foreground">أعلى</p>
            <p className="font-mono font-semibold text-lg">{stockData.high.toFixed(2)}</p>
          </div>
          <div className="p-3 bg-secondary/50 rounded-lg">
            <p className="text-sm text-muted-foreground">أدنى</p>
            <p className="font-mono font-semibold text-lg">{stockData.low.toFixed(2)}</p>
          </div>
          <div className="p-3 bg-secondary/50 rounded-lg">
            <p className="text-sm text-muted-foreground">حجم التداول</p>
            <p className="font-mono font-semibold text-lg">{stockData.volume}</p>
          </div>
           <div className="p-3 bg-secondary/50 rounded-lg">
            <p className="text-sm text-muted-foreground">القيمة السوقية</p>
            <p className="font-mono font-semibold text-lg">{stockData.marketCap}</p>
          </div>
        </div>
        <p className="text-xs text-muted-foreground mt-6 text-center">
            آخر تحديث: {stockData.timestamp.toLocaleString('ar-SA-u-ca-gregory-nu-latn', { dateStyle: 'medium', timeStyle: 'short' })}.
            البيانات محدثة من مصادر موثوقة وهي لأغراض إعلامية فقط.
        </p>
      </CardContent>
    </Card>
  );
}
