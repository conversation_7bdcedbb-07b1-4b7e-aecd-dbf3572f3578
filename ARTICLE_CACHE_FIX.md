# حل مشكلة عدم تحديث محتوى المقالات

## المشكلة
عند تحديث محتوى المقالات من لوحة الأدمين، لا يظهر المحتوى المحدث على الموقع بسبب التخزين المؤقت (caching) في Next.js.

## السبب
1. **التخزين المؤقت في Next.js**: الصفحات يتم تخزينها مؤقتاً لتحسين الأداء
2. **إعدادات Cache-Control**: الصفحات تُخزن لمدة ساعة واحدة
3. **عدم إعادة التحقق**: لا يتم إعادة التحقق من المحتوى بعد التحديث

## الحل المطبق

### 1. تحديث إعدادات التخزين المؤقت
```typescript
// في next.config.ts
{
  source: '/articles/(.*)',
  headers: [
    {
      key: 'Cache-Control',
      value: 'public, max-age=300, s-maxage=600, stale-while-revalidate=3600',
    },
  ],
},
```

### 2. إضافة إعدادات Dynamic Rendering
```typescript
// في src/app/articles/[slug]/page.tsx
export const dynamic = 'force-dynamic';
export const revalidate = 0;
```

### 3. إنشاء API endpoint لإعادة التحقق
```typescript
// src/app/api/revalidate/route.ts
export async function POST(request: NextRequest) {
  // إعادة التحقق من صفحة معينة أو جميع الصفحات
  revalidatePath(path);
}
```

### 4. تحديث دالة generateMetadata
```typescript
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  // جلب المقالة من قاعدة البيانات أولاً
  let article = await getArticleBySlug(slug);
  
  // العودة للمقالات الثابتة إذا لم توجد
  if (!article) {
    article = articles[slug as keyof typeof articles];
  }
  
  return {
    title: article.seoTitle || article.title,
    description: article.seoDescription || article.description,
    keywords: article.seoKeywords || [article.category, 'مقالات عربية'],
  };
}
```

### 5. إضافة إعادة التحقق التلقائي في الأدمين
```typescript
// في handleSubmit بعد حفظ المقال
await fetch(`/api/revalidate`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    path: `/articles/${formData.slug}`,
    secret: process.env.NEXT_PUBLIC_REVALIDATION_SECRET
  }),
});
```

## متغيرات البيئة المطلوبة
```env
REVALIDATION_SECRET=your-secret-key-here-change-in-production
NEXT_PUBLIC_REVALIDATION_SECRET=your-secret-key-here-change-in-production
```

## كيفية الاستخدام

### إعادة التحقق اليدوي
```bash
# إعادة التحقق من مقالة معينة
curl "https://adawat.org/api/revalidate?path=/articles/how-to-calculate-zakat&secret=YOUR_SECRET"

# إعادة التحقق من جميع المقالات
curl "https://adawat.org/api/revalidate?secret=YOUR_SECRET"
```

### إعادة التحقق التلقائي
- يتم تلقائياً عند حفظ أو تحديث المقالة من لوحة الأدمين
- يتم إعادة التحقق من الصفحة المحددة فقط

## الفوائد
1. **تحديث فوري**: المحتوى المحدث يظهر فوراً
2. **أداء محسن**: التخزين المؤقت لا يزال يعمل للزوار
3. **مرونة**: يمكن إعادة التحقق يدوياً أو تلقائياً
4. **أمان**: محمي بسر لمنع الاستخدام غير المصرح به

## ملاحظات مهمة
- تأكد من تغيير `REVALIDATION_SECRET` في البيئة الإنتاجية
- إعدادات التخزين المؤقت محسنة لتوازن بين الأداء والتحديث
- يمكن تخصيص مدة التخزين المؤقت حسب الحاجة

## اختبار الحل
1. قم بتحديث مقالة من لوحة الأدمين
2. تحقق من ظهور المحتوى المحدث فوراً
3. تحقق من سجلات الخادم لرؤية رسائل إعادة التحقق
