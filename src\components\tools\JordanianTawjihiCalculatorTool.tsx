
'use client';

import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { PlusCircle, Trash2, Calculator } from 'lucide-react';
import { useState } from 'react';

const requiredNumber = (message = "هذا الحقل مطلوب.") =>
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const subjectSchema = z.object({
  name: z.string().min(1, 'اسم المادة مطلوب.'),
  grade: requiredNumber().min(0, "الدرجة لا يمكن أن تكون سالبة.").max(100, "الدرجة لا يمكن أن تزيد عن 100."),
});

const formSchema = z.object({
  subjects: z.array(subjectSchema).min(1, 'يجب إضافة مادة واحدة على الأقل.'),
});

type FormValues = z.infer<typeof formSchema>;

const initialSubjects = [
  { name: 'التربية الإسلامية' },
  { name: 'اللغة العربية' },
  { name: 'اللغة الإنجليزية' },
  { name: 'تاريخ الأردن' },
  { name: 'الرياضيات' },
  { name: 'الفيزياء' },
  { name: 'الكيمياء' },
];

export function JordanianTawjihiCalculatorTool() {
  const [result, setResult] = useState<{ average: number } | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      subjects: initialSubjects.map(s => ({...s, grade: '' as any})),
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "subjects",
  });

  const onSubmit = (data: FormValues) => {
    const totalGrades = data.subjects.reduce((sum, subject) => sum + subject.grade, 0);
    const average = totalGrades / data.subjects.length;
    setResult({ average });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة معدل التوجيهي الأردني</CardTitle>
        <CardDescription>أدخل درجاتك في المواد لحساب معدلك النهائي في التوجيهي (من 100).</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <div className="grid grid-cols-[1fr,120px,auto] gap-2 items-center text-sm font-medium text-muted-foreground border-b pb-2">
                <span>اسم المادة</span>
                <span className="text-center">الدرجة (من 100)</span>
                <span className="text-center">حذف</span>
              </div>
              {fields.map((field, index) => (
                <div key={field.id} className="grid grid-cols-[1fr,120px,auto] gap-2 items-start">
                  <FormField
                    name={`subjects.${index}.name`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input placeholder={`مادة ${index + 1}`} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    name={`subjects.${index}.grade`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input type="number" min="0" max="100" placeholder="مثال 85" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="text-center">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => remove(index)}
                      className="text-destructive hover:text-destructive"
                      disabled={fields.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <Button
              type="button"
              variant="outline"
              onClick={() => append({ name: '', grade: '' as any })}
              className="w-full border-dashed"
            >
              <PlusCircle className="ml-2 h-4 w-4" />
              إضافة مادة
            </Button>

            <div className="flex flex-col items-center space-y-4 pt-6 border-t">
              <Button type="submit" size="lg" className="w-full sm:w-auto px-12">
                <Calculator className="ml-2 h-4 w-4" />
                احسب المعدل
              </Button>

              {result && (
                <div className="text-center p-6 bg-primary/10 rounded-lg border-2 border-primary/20 w-full">
                  <p className="text-sm text-primary/80 mb-2">معدل التوجيهي النهائي</p>
                  <p className="text-5xl font-bold text-primary font-mono">
                    {result.average.toFixed(2)}%
                  </p>
                </div>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

// أداة حساب معدل التوجيهي 2008
const initialSubjects2008 = [
  { name: 'التربية الإسلامية' },
  { name: 'اللغة العربية' },
  { name: 'اللغة الإنجليزية' },
  { name: 'الرياضيات' },
  { name: 'الفيزياء' },
  { name: 'الكيمياء' },
  { name: 'الأحياء' },
  { name: 'علوم الأرض والبيئة' },
];

export function Tawjihi2008CalculatorTool() {
  const [result, setResult] = useState<{ average: number; percentage: number } | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      subjects: initialSubjects2008.map(s => ({...s, grade: '' as any})),
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "subjects",
  });

  const onSubmit = (data: FormValues) => {
    const totalGrades = data.subjects.reduce((sum, subject) => sum + subject.grade, 0);
    const average = totalGrades / data.subjects.length;
    // حساب النسبة المئوية من 1400 (الدرجة الكاملة لـ 8 مواد × 100 لكل مادة = 800، ولكن في نظام 2008 كانت الدرجة الكاملة 1400)
    const maxScore = 1400;
    const actualTotal = data.subjects.length * 100; // الدرجة الفعلية الكاملة بناءً على عدد المواد المدخلة
    const percentage = (totalGrades / actualTotal) * 100;

    setResult({ average, percentage });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة معدل التوجيهي 2008</CardTitle>
        <CardDescription>أدخل درجاتك في المواد لحساب معدلك النهائي في التوجيهي لعام 2008 (النظام القديم).</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">ملاحظة مهمة:</h4>
          <p className="text-blue-800 text-sm">
            هذه الحاسبة مخصصة لنظام التوجيهي الأردني لعام 2008 والسنوات المشابهة.
            يرجى إدخال الدرجات كما هي مكتوبة في الشهادة (من 100 لكل مادة).
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <div className="grid grid-cols-[1fr,120px,auto] gap-2 items-center text-sm font-medium text-muted-foreground border-b pb-2">
                <span>اسم المادة</span>
                <span className="text-center">الدرجة (من 100)</span>
                <span className="text-center">حذف</span>
              </div>
              {fields.map((field, index) => (
                <div key={field.id} className="grid grid-cols-[1fr,120px,auto] gap-2 items-start">
                  <FormField
                    name={`subjects.${index}.name`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input placeholder={`مادة ${index + 1}`} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    name={`subjects.${index}.grade`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input type="number" min="0" max="100" placeholder="مثال 85" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="text-center">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => remove(index)}
                      className="text-destructive hover:text-destructive"
                      disabled={fields.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <Button
              type="button"
              variant="outline"
              onClick={() => append({ name: '', grade: '' as any })}
              className="w-full border-dashed"
            >
              <PlusCircle className="ml-2 h-4 w-4" />
              إضافة مادة
            </Button>

            <div className="flex flex-col items-center space-y-4 pt-6 border-t">
              <Button type="submit" size="lg" className="w-full sm:w-auto px-12">
                <Calculator className="ml-2 h-4 w-4" />
                احسب المعدل
              </Button>

              {result && (
                <div className="space-y-4 w-full">
                  <div className="text-center p-6 bg-primary/10 rounded-lg border-2 border-primary/20">
                    <p className="text-sm text-primary/80 mb-2">المعدل العام</p>
                    <p className="text-4xl font-bold text-primary font-mono">
                      {result.average.toFixed(2)}%
                    </p>
                  </div>

                  <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                    <p className="text-sm text-green-700 mb-1">النسبة المئوية</p>
                    <p className="text-2xl font-bold text-green-800 font-mono">
                      {result.percentage.toFixed(2)}%
                    </p>
                  </div>
                </div>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
