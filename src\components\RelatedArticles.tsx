import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Calendar, Clock, ArrowLeft, BookOpen } from 'lucide-react';

interface Article {
  id: string;
  title: string;
  slug: string;
  description: string;
  createdAt: Date;
  readTime: string;
  category: string;
}

interface RelatedArticlesProps {
  articles: Article[];
  currentArticleId: string;
  title?: string;
}

export function RelatedArticles({ 
  articles, 
  currentArticleId, 
  title = "مقالات ذات صلة" 
}: RelatedArticlesProps) {
  // Filter out current article and limit to 3 related articles
  const relatedArticles = articles
    .filter(article => article.id !== currentArticleId)
    .slice(0, 3);

  if (relatedArticles.length === 0) {
    return null;
  }

  return (
    <Card className="mt-8 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border-blue-200 shadow-lg">
      <CardContent className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-3 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl shadow-md">
            <BookOpen className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-blue-800 mb-1">{title}</h3>
            <p className="text-sm text-blue-600">
              اقرأ المزيد من المقالات المفيدة
            </p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {relatedArticles.map((article) => (
            <Link
              key={article.id}
              href={`/articles/${article.slug}`}
              className="group block"
            >
              <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-blue-300 bg-white/70 backdrop-blur-sm">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                      {article.category}
                    </span>
                    <ArrowLeft className="h-4 w-4 text-muted-foreground transition-transform group-hover:translate-x-[-4px] group-hover:text-blue-600" />
                  </div>
                  <CardTitle className="font-headline text-base leading-tight group-hover:text-blue-700 transition-colors">
                    {article.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                    {article.description}
                  </p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{article.createdAt.toLocaleDateString('ar-SA-u-nu-latn')}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{article.readTime}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
        
        <div className="mt-6 text-center">
          <Link href="/articles" className="text-blue-600 hover:underline text-sm font-medium">
            استكشف جميع المقالات ←
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
